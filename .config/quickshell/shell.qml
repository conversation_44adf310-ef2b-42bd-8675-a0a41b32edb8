import QtQuick
import Quickshell
import "bar"

ShellRoot {
    Loader {
        id: debuggerLoader
        source: "debugger.qml"
    }

    Component.onCompleted: {
    }

    Bar {
        id: bar0
        targetScreen: Quickshell.screens[0]
        screenIndex: 0

        Component.onCompleted: {
        }
    }

    Bar {
        id: bar1
        targetScreen: Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
        screenIndex: 1

        Component.onCompleted: {
        }
    }
    Loader {
        id: mprisOverlay0
        source: "bar/MprisOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens[0]
            item.barWindow = bar0
            item.overlayVisible = Qt.binding(() => bar0.showMprisOverlay)
        }
    }

    Loader {
        id: calendarOverlay0
        source: "bar/CalendarOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens[0]
            item.barWindow = bar0
            item.overlayVisible = Qt.binding(() => bar0.showCalendarOverlay)
        }
    }

    Loader {
        id: mprisOverlay1
        source: "bar/MprisOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
            item.barWindow = bar1
            item.overlayVisible = Qt.binding(() => bar1.showMprisOverlay)
        }
    }

    Loader {
        id: calendarOverlay1
        source: "bar/CalendarOverlay.qml"
        onLoaded: {
            item.screen = Quickshell.screens.length > 1 ? Quickshell.screens[1] : Quickshell.screens[0]
            item.barWindow = bar1
            item.overlayVisible = Qt.binding(() => bar1.showCalendarOverlay)
        }
    }

}
