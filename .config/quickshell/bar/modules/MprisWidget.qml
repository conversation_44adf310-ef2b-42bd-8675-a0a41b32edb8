import QtQuick
import QtQuick.Layouts
import Quickshell.Services.Mpris
import "../modules"

Rectangle {
    id: mprisWidget
    objectName: "mprisWidget"
    width: visible ? (mprisContent.width + 16) : 0
    height: 18
    radius: 9
    color: mouseArea.containsMouse ? Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.2) : "transparent"

    visible: activePlayer !== null

    signal showMprisOverlayRequested()
    signal hideMprisOverlayRequested()

    readonly property var activePlayer: {
        if (!Mpris.players || !Mpris.players.values) {
            return null
        }

        var playingPlayer = null
        var pausedPlayer = null
        var anyPlayer = null

        try {
            var playersList = Mpris.players.values

            for (var i = 0; i < playersList.length; i++) {
                var player = playersList[i]

                if (player && typeof player === "object" && player.identity) {
                    if (!anyPlayer) anyPlayer = player

                    if (player.playbackState === MprisPlaybackState.Playing) {
                        playingPlayer = player
                    } else if (player.playbackState === MprisPlaybackState.Paused) {
                        pausedPlayer = player
                    }
                }
            }
        } catch (e) {
        }

        return playingPlayer || pausedPlayer || anyPlayer
    }

    Theme {
        id: theme
    }

    RowLayout {
        id: mprisContent
        anchors.centerIn: parent
        spacing: 8

        Text {
            id: playIcon
            text: {
                if (!activePlayer) return "󰝚"
                switch (activePlayer.playbackState) {
                    case MprisPlaybackState.Playing: return "󰏤"
                    case MprisPlaybackState.Paused: return "󰐊"
                    default: return "󰓛"
                }
            }
            color: theme.textPrimary
            font.pixelSize: 14
            font.family: "JetBrains Mono, monospace"
        }

        Text {
            id: trackInfo
            text: {
                if (!activePlayer) return "No media"
                var title = activePlayer.trackTitle || "Unknown"
                var artist = activePlayer.trackArtists && activePlayer.trackArtists.length > 0
                    ? activePlayer.trackArtists[0] : ""

                if (artist) {
                    return title + " • " + artist
                } else {
                    return title
                }
            }
            color: theme.textSecondary
            font.pixelSize: 11
            font.family: "JetBrains Mono, monospace"

            Layout.maximumWidth: 200
            elide: Text.ElideRight
        }
    }

    MouseArea {
        id: mouseArea
        anchors.fill: parent
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        hoverEnabled: true

        onClicked: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                showMprisOverlayRequested()
            } else if (mouse.button === Qt.RightButton && activePlayer) {
                if (activePlayer.playbackState === MprisPlaybackState.Playing) {
                    activePlayer.pause()
                } else {
                    activePlayer.play()
                }
            }
        }
    }

    Behavior on color {
        ColorAnimation { duration: 200 }
    }

    Behavior on width {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }
}