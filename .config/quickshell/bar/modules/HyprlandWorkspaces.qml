import QtQuick
import QtQuick.Layouts
import Quickshell.Hyprland

Item {
    id: workspacesContainer

    // Properties for multi-monitor support
    property int screenIndex: 0
    property var workspaceRanges: [
        [1, 6],  // Screen 0: workspaces 1-6
        [7, 9]   // Screen 1: workspaces 7-9
    ]
    property var currentRange: screenIndex < workspaceRanges.length ?
        workspaceRanges[screenIndex] : [1, 10]

    // Theme instance
    Theme {
        id: theme
    }
    
    // Force refresh counter to trigger property updates
    property int refreshCounter: 0

    // Count of workspaces with windows or focused workspace
    property int activeWorkspaceCount: {
        // Include refreshCounter to force re-evaluation
        refreshCounter

        var count = 0
        var workspaces = Hyprland.workspaces

        if (workspaces && workspaces.values) {
            var workspaceList = workspaces.values
            for (var i = 0; i < workspaceList.length; i++) {
                var workspace = workspaceList[i]
                // Count workspace if it has windows or is the focused workspace
                var hasWindows = workspace.lastIpcObject && workspace.lastIpcObject.windows > 0
                var isFocused = workspace.id === Hyprland.focusedWorkspace?.id

                if (hasWindows || isFocused) {
                    count++
                }
            }
        }
        return count
    }

    // Helper function to check if workspace should be shown
    function shouldShowWorkspace(workspace) {
        if (!workspace) return false

        // Check if workspace is in the current screen's range
        var workspaceId = workspace.id
        var inRange = workspaceId >= currentRange[0] && workspaceId <= currentRange[1]
        if (!inRange) return false

        // Force refresh of workspace data
        Hyprland.refreshWorkspaces()

        var hasWindows = workspace.lastIpcObject && workspace.lastIpcObject.windows > 0
        var isFocused = workspace.id === Hyprland.focusedWorkspace?.id
        return hasWindows || isFocused
    }
    
    // Dynamic width based on active workspaces
    width: workspaceRow.width
    height: 20
    
    // Workspace icons mapping
    property var workspaceIcons: {
        "1": "",
        "2": "󰈹",
        "3": "󰊖",
        "4": "",
        "5": "󰴓",
        "6": "",
        "7": "󰯙",
        "8": "",
        "9": "󰍳"
    }
    
    Row {
        id: workspaceRow
        anchors.centerIn: parent
        spacing: 4
        
        // Left bracket
        Text {
            id: leftBracket
            text: "『"
            color: theme.textAccent
            font.pixelSize: 16
            font.family: "JetBrains Mono Nerd Font, monospace"
            font.weight: Font.Bold
            anchors.verticalCenter: parent.verticalCenter

            // Dimmed when no workspaces, full opacity when workspaces exist
            opacity: activeWorkspaceCount > 0 ? 1.0 : 0.4

            // Enhanced scaling animation for bracket expansion
            scale: activeWorkspaceCount > 0 ? 1.0 : 0.9

            Behavior on opacity {
                NumberAnimation { duration: 400; easing.type: Easing.OutCubic }
            }

            Behavior on scale {
                NumberAnimation {
                    duration: 500
                    easing.type: Easing.OutBack
                    easing.overshoot: 1.2
                }
            }
        }
        
        // Workspace indicators container
        Row {
            id: workspaceIndicators
            spacing: 6
            anchors.verticalCenter: parent.verticalCenter
            
            // Animate width changes
            Behavior on width {
                NumberAnimation { 
                    duration: 400
                    easing.type: Easing.OutBack
                    easing.overshoot: 1.1
                }
            }
            
            Repeater {
                id: workspaceRepeater
                model: Hyprland.workspaces

                Text {
                    id: workspaceIndicator

                    // Only show if workspace should be visible
                    visible: shouldShowWorkspace(modelData)
                    width: visible ? 16 : 0
                    height: 16

                    // Workspace icon text
                    text: workspaceIcons[modelData.id.toString()] || modelData.id.toString()
                    color: {
                        if (modelData.id === Hyprland.focusedWorkspace?.id) {
                            return theme.textPrimary
                        } else {
                            return theme.textSecondary
                        }
                    }
                    font.pixelSize: 14
                    font.family: "JetBrains Mono Nerd Font, monospace"
                    font.weight: modelData.id === Hyprland.focusedWorkspace?.id ? Font.Bold : Font.Medium

                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                    
                    // Enhanced glow effect for active workspace (circle around icon)
                    // Only show when there are active workspaces (not just empty brackets)
                    Rectangle {
                        id: glowEffect
                        anchors.centerIn: parent
                        width: 24
                        height: 24
                        radius: 12
                        color: "transparent"
                        border.width: (modelData.id === Hyprland.focusedWorkspace?.id && shouldShowWorkspace(modelData) && (modelData.lastIpcObject?.windows > 0)) ? 2 : 0
                        border.color: Qt.rgba(theme.workspaceActive.r, theme.workspaceActive.g, theme.workspaceActive.b, 0.6)
                        opacity: (modelData.id === Hyprland.focusedWorkspace?.id && shouldShowWorkspace(modelData) && (modelData.lastIpcObject?.windows > 0)) ? 1 : 0

                        // Pulsing glow animation (only when workspace has windows)
                        SequentialAnimation on border.color {
                            running: modelData.id === Hyprland.focusedWorkspace?.id && shouldShowWorkspace(modelData) && (modelData.lastIpcObject?.windows > 0)
                            loops: Animation.Infinite
                            ColorAnimation {
                                to: Qt.rgba(theme.workspaceActive.r, theme.workspaceActive.g, theme.workspaceActive.b, 0.8)
                                duration: 1500
                                easing.type: Easing.InOutSine
                            }
                            ColorAnimation {
                                to: Qt.rgba(theme.workspaceActive.r, theme.workspaceActive.g, theme.workspaceActive.b, 0.4)
                                duration: 1500
                                easing.type: Easing.InOutSine
                            }
                            PauseAnimation { duration: 500 }
                        }

                        Behavior on opacity {
                            NumberAnimation { duration: 400; easing.type: Easing.OutCubic }
                        }

                        Behavior on border.width {
                            NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
                        }
                    }
                    
                    // Enhanced pulsing animation for active workspace (only when workspace has windows)
                    SequentialAnimation on scale {
                        id: pulseAnimation
                        running: modelData.id === Hyprland.focusedWorkspace?.id && !mouseArea.containsMouse && shouldShowWorkspace(modelData) && (modelData.lastIpcObject?.windows > 0)
                        loops: Animation.Infinite
                        NumberAnimation {
                            to: 1.08
                            duration: 1200
                            easing.type: Easing.InOutSine
                        }
                        NumberAnimation {
                            to: 1.0
                            duration: 1200
                            easing.type: Easing.InOutSine
                        }
                        PauseAnimation { duration: 300 }
                    }
                    
                    // Smooth color transitions
                    Behavior on color {
                        ColorAnimation { duration: 250 }
                    }
                    
                    // Enhanced interactive scaling with bounce
                    scale: {
                        if (mouseArea.containsMouse) {
                            return modelData.id === Hyprland.focusedWorkspace?.id ? 1.3 : 1.25
                        } else {
                            return 1.0
                        }
                    }

                    Behavior on scale {
                        NumberAnimation {
                            duration: 300
                            easing.type: Easing.OutBack
                            easing.overshoot: 1.4
                        }
                    }

                    // Enhanced rotation for hover effect
                    rotation: {
                        if (mouseArea.containsMouse) {
                            return modelData.id === Hyprland.focusedWorkspace?.id ? 5 : 3
                        } else {
                            return 0
                        }
                    }

                    Behavior on rotation {
                        NumberAnimation {
                            duration: 250
                            easing.type: Easing.OutBack
                            easing.overshoot: 1.1
                        }
                    }
                    
                    MouseArea {
                        id: mouseArea
                        anchors.fill: parent
                        hoverEnabled: true
                        
                        onClicked: {
                            Hyprland.dispatch("workspace " + modelData.id)
                        }
                    }
                    
                    // Entrance animation
                    NumberAnimation {
                        id: entranceAnimation
                        target: workspaceIndicator
                        property: "scale"
                        from: 0
                        to: 1
                        duration: 500
                        easing.type: Easing.OutBack
                        easing.overshoot: 1.8
                    }
                    
                    Component.onCompleted: {
                        entranceAnimation.start()
                    }
                }
            }
        }
        
        // Right bracket
        Text {
            id: rightBracket
            text: "』"
            color: theme.textAccent
            font.pixelSize: 16
            font.family: "JetBrains Mono Nerd Font, monospace"
            font.weight: Font.Bold
            anchors.verticalCenter: parent.verticalCenter

            // Dimmed when no workspaces, full opacity when workspaces exist
            opacity: activeWorkspaceCount > 0 ? 1.0 : 0.4

            // Enhanced scaling animation for bracket expansion
            scale: activeWorkspaceCount > 0 ? 1.0 : 0.9

            Behavior on opacity {
                NumberAnimation { duration: 400; easing.type: Easing.OutCubic }
            }

            Behavior on scale {
                NumberAnimation {
                    duration: 500
                    easing.type: Easing.OutBack
                    easing.overshoot: 1.2
                }
            }
        }
    }
    
    // Enhanced bracket expansion animation
    PropertyAnimation {
        id: bracketExpandAnimation
        target: workspaceRow
        property: "spacing"
        duration: 500
        easing.type: Easing.OutBack
        easing.overshoot: 1.3
    }

    // Container scale animation
    PropertyAnimation {
        id: containerScaleAnimation
        target: workspacesContainer
        property: "scale"
        duration: 600
        easing.type: Easing.OutElastic
        easing.amplitude: 1.0
        easing.period: 0.5
    }

    // Monitor workspace changes for enhanced bracket animation
    onActiveWorkspaceCountChanged: {
        // Trigger enhanced bracket expansion animation
        bracketExpandAnimation.from = workspaceRow.spacing
        bracketExpandAnimation.to = activeWorkspaceCount > 0 ? 6 : 2
        bracketExpandAnimation.start()

        // Scale effect for dramatic expansion
        containerScaleAnimation.from = activeWorkspaceCount > 0 ? 0.95 : 1.0
        containerScaleAnimation.to = 1.0
        containerScaleAnimation.start()

        // console.log("Active workspaces updated:", activeWorkspaceCount)
    }
    
    // Optional: Debug timer (disabled by default)
    // Timer {
    //     interval: 5000
    //     running: false  // Set to true for debugging
    //     repeat: true
    //     onTriggered: {
    //         console.log("=== Workspace Debug ===")
    //         console.log("Active workspaces:", activeWorkspaceCount)
    //         console.log("Focused workspace:", Hyprland.focusedWorkspace?.id)
    //     }
    // }

    // Force refresh when Hyprland properties change
    Connections {
        target: Hyprland

        function onFocusedWorkspaceChanged() {
            workspacesContainer.forceRefresh()
        }
    }

    // Store previous workspace state for comparison
    property var previousWorkspaceState: ({})

    // Force refresh function
    function forceRefresh() {
        // Force workspace data refresh
        Hyprland.refreshWorkspaces()
        // Increment counter to trigger property re-evaluation
        refreshCounter++
    }

    // Check if workspace state has actually changed
    function hasWorkspaceStateChanged() {
        var currentState = {}
        var workspaces = Hyprland.workspaces

        if (workspaces && workspaces.values) {
            var workspaceList = workspaces.values
            for (var i = 0; i < workspaceList.length; i++) {
                var workspace = workspaceList[i]
                var windowCount = workspace.lastIpcObject ? workspace.lastIpcObject.windows : 0
                currentState[workspace.id] = {
                    windows: windowCount,
                    focused: workspace.id === Hyprland.focusedWorkspace?.id
                }
            }
        }

        // Compare with previous state
        var stateChanged = JSON.stringify(currentState) !== JSON.stringify(previousWorkspaceState)
        if (stateChanged) {
            previousWorkspaceState = currentState
        }

        return stateChanged
    }

    // Smart refresh timer - only updates when state actually changes
    Timer {
        id: smartRefreshTimer
        interval: 100  // Check every 100ms
        running: true
        repeat: true
        onTriggered: {
            if (workspacesContainer.hasWorkspaceStateChanged()) {
                workspacesContainer.forceRefresh()
            }
        }
    }

    // Component initialization
    Component.onCompleted: {
        // Initial refresh
        forceRefresh()
        // Start smart monitoring
        smartRefreshTimer.start()
    }
    
    // Container glow effect removed for cleaner appearance
}
