import QtQuick
import QtQuick.Layouts

Rectangle {
    id: calendarWidget
    color: "transparent"

    property date currentDate: new Date()
    property date selectedDate: new Date()
    property int currentMonth: currentDate.getMonth()
    property int currentYear: currentDate.getFullYear()

    Theme {
        id: theme
    }

    function getDaysInMonth(month, year) {
        return new Date(year, month + 1, 0).getDate()
    }

    function getFirstDayOfMonth(month, year) {
        return new Date(year, month, 1).getDay()
    }

    function isToday(day, month, year) {
        var today = new Date()
        return day === today.getDate() && 
               month === today.getMonth() && 
               year === today.getFullYear()
    }

    function isSelected(day, month, year) {
        return day === selectedDate.getDate() && 
               month === selectedDate.getMonth() && 
               year === selectedDate.getFullYear()
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 8

        RowLayout {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            spacing: 16

            Text {
                text: Qt.formatDate(new Date(currentYear, currentMonth, 1), "MMM yyyy").toUpperCase()
                color: theme.textPrimary
                font.pixelSize: 16
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Bold
                Layout.alignment: Qt.AlignVCenter
            }

            Text {
                text: "‹"
                color: theme.textSecondary
                font.pixelSize: 16
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Bold
                Layout.alignment: Qt.AlignVCenter

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        if (currentMonth === 0) {
                            currentMonth = 11
                            currentYear--
                        } else {
                            currentMonth--
                        }
                    }
                }
            }

            Text {
                text: "TODAY"
                color: theme.textSecondary
                font.pixelSize: 12
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Medium
                Layout.alignment: Qt.AlignVCenter

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        var today = new Date()
                        currentMonth = today.getMonth()
                        currentYear = today.getFullYear()
                        selectedDate = today
                    }
                }
            }

            Text {
                text: "›"
                color: theme.textSecondary
                font.pixelSize: 16
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Bold
                Layout.alignment: Qt.AlignVCenter

                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        if (currentMonth === 11) {
                            currentMonth = 0
                            currentYear++
                        } else {
                            currentMonth++
                        }
                    }
                }
            }
        }

        GridLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            columns: 7
            rowSpacing: 2
            columnSpacing: 2

            Repeater {
                model: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"]
                
                Text {
                    text: modelData
                    color: theme.textSecondary
                    font.pixelSize: 10
                    font.family: "JetBrains Mono, monospace"
                    font.weight: Font.Medium
                    horizontalAlignment: Text.AlignHCenter
                    Layout.fillWidth: true
                    Layout.preferredHeight: 16
                }
            }

            Repeater {
                model: 42

                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 20
                    radius: 4
                    
                    property int dayNumber: {
                        var firstDay = getFirstDayOfMonth(currentMonth, currentYear)
                        var daysInMonth = getDaysInMonth(currentMonth, currentYear)
                        var dayIndex = index - firstDay
                        
                        if (dayIndex < 0 || dayIndex >= daysInMonth) {
                            return -1
                        }
                        return dayIndex + 1
                    }
                    
                    property bool isValidDay: dayNumber > 0
                    property bool isTodayDay: isValidDay && isToday(dayNumber, currentMonth, currentYear)
                    property bool isSelectedDay: isValidDay && isSelected(dayNumber, currentMonth, currentYear)
                    property bool isHovered: false
                    
                    color: {
                        if (!isValidDay) return "transparent"
                        if (isSelectedDay) return theme.pink
                        if (isHovered) return theme.blue
                        return "transparent"
                    }
                    
                    border.color: isTodayDay ? theme.accent : "transparent"
                    border.width: isTodayDay ? 1 : 0
                    
                    Text {
                        anchors.centerIn: parent
                        text: parent.isValidDay ? parent.dayNumber : ""
                        color: {
                            if (!parent.isValidDay) return "transparent"
                            if (parent.isSelectedDay) return "#000000"
                            if (parent.isHovered) return "#000000"
                            return theme.textPrimary
                        }
                        font.pixelSize: 10
                        font.family: "JetBrains Mono, monospace"
                        font.weight: parent.isTodayDay || parent.isSelectedDay ? Font.Bold : Font.Normal
                    }
                    
                    MouseArea {
                        id: dayMouseArea
                        anchors.fill: parent
                        hoverEnabled: parent.isValidDay
                        enabled: parent.isValidDay

                        onEntered: {
                            if (parent.isValidDay) {
                                parent.isHovered = true
                            }
                        }

                        onExited: {
                            parent.isHovered = false
                        }

                        onClicked: {
                            if (parent.isValidDay) {
                                selectedDate = new Date(currentYear, currentMonth, parent.dayNumber)
                            }
                        }
                    }
                    
                    Behavior on color {
                        ColorAnimation { duration: 150 }
                    }
                    
                    Behavior on border.color {
                        ColorAnimation { duration: 150 }
                    }
                }
            }
        }
    }

    Timer {
        interval: 60000
        running: true
        repeat: true
        onTriggered: {
            currentDate = new Date()
        }
    }
}
