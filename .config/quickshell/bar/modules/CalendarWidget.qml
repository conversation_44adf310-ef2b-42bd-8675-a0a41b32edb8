import QtQuick
import QtQuick.Layouts

Rectangle {
    id: calendarWidget
    color: "transparent"

    property date currentDate: new Date()
    property date selectedDate: new Date()
    property int currentMonth: currentDate.getMonth()
    property int currentYear: currentDate.getFullYear()

    Theme {
        id: theme
    }

    function getDaysInMonth(month, year) {
        return new Date(year, month + 1, 0).getDate()
    }

    function getFirstDayOfMonth(month, year) {
        return new Date(year, month, 1).getDay()
    }

    function isToday(day, month, year) {
        var today = new Date()
        return day === today.getDate() && 
               month === today.getMonth() && 
               year === today.getFullYear()
    }

    function isSelected(day, month, year) {
        return day === selectedDate.getDate() && 
               month === selectedDate.getMonth() && 
               year === selectedDate.getFullYear()
    }

    ColumnLayout {
        anchors.fill: parent
        spacing: 12

        RowLayout {
            Layout.fillWidth: true
            Layout.preferredHeight: 32
            spacing: 8

            Text {
                text: Qt.formatDate(new Date(currentYear, currentMonth, 1), "MMM yyyy").toUpperCase()
                color: theme.textPrimary
                font.pixelSize: 14
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Bold
                Layout.alignment: Qt.AlignVCenter
            }

            Item { Layout.fillWidth: true }

            Text {
                text: "‹"
                color: theme.textSecondary
                font.pixelSize: 12
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Bold
                Layout.alignment: Qt.AlignVCenter

                MouseArea {
                    anchors.fill: parent
                    anchors.margins: -4
                    onClicked: {
                        if (currentMonth === 0) {
                            currentMonth = 11
                            currentYear--
                        } else {
                            currentMonth--
                        }
                    }
                }
            }

            Text {
                text: "TODAY"
                color: theme.textSecondary
                font.pixelSize: 10
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Medium
                Layout.alignment: Qt.AlignVCenter

                MouseArea {
                    anchors.fill: parent
                    anchors.margins: -4
                    onClicked: {
                        var today = new Date()
                        currentMonth = today.getMonth()
                        currentYear = today.getFullYear()
                        selectedDate = today
                    }
                }
            }

            Text {
                text: "›"
                color: theme.textSecondary
                font.pixelSize: 12
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Bold
                Layout.alignment: Qt.AlignVCenter

                MouseArea {
                    anchors.fill: parent
                    anchors.margins: -4
                    onClicked: {
                        if (currentMonth === 11) {
                            currentMonth = 0
                            currentYear++
                        } else {
                            currentMonth++
                        }
                    }
                }
            }
        }

        GridLayout {
            Layout.fillWidth: true
            Layout.fillHeight: true
            columns: 7
            rowSpacing: 4
            columnSpacing: 4

            Repeater {
                model: ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"]

                Text {
                    text: modelData
                    color: theme.textSecondary
                    font.pixelSize: 8
                    font.family: "JetBrains Mono, monospace"
                    font.weight: Font.Medium
                    horizontalAlignment: Text.AlignHCenter
                    Layout.fillWidth: true
                    Layout.preferredHeight: 16
                }
            }

            Repeater {
                model: 42

                Rectangle {
                    Layout.fillWidth: true
                    Layout.fillHeight: true
                    Layout.preferredHeight: 24
                    radius: 4
                    
                    property var dayInfo: {
                        var firstDay = getFirstDayOfMonth(currentMonth, currentYear)
                        var daysInMonth = getDaysInMonth(currentMonth, currentYear)
                        var dayIndex = index - firstDay

                        if (dayIndex < 0) {
                            var prevMonth = currentMonth === 0 ? 11 : currentMonth - 1
                            var prevYear = currentMonth === 0 ? currentYear - 1 : currentYear
                            var prevMonthDays = getDaysInMonth(prevMonth, prevYear)
                            return {
                                day: prevMonthDays + dayIndex + 1,
                                month: prevMonth,
                                year: prevYear,
                                isCurrentMonth: false
                            }
                        } else if (dayIndex >= daysInMonth) {
                            var nextMonth = currentMonth === 11 ? 0 : currentMonth + 1
                            var nextYear = currentMonth === 11 ? currentYear + 1 : currentYear
                            return {
                                day: dayIndex - daysInMonth + 1,
                                month: nextMonth,
                                year: nextYear,
                                isCurrentMonth: false
                            }
                        } else {
                            return {
                                day: dayIndex + 1,
                                month: currentMonth,
                                year: currentYear,
                                isCurrentMonth: true
                            }
                        }
                    }

                    property int dayNumber: dayInfo.day
                    property bool isCurrentMonth: dayInfo.isCurrentMonth
                    property bool isTodayDay: isToday(dayInfo.day, dayInfo.month, dayInfo.year)
                    property bool isSelectedDay: isSelected(dayInfo.day, dayInfo.month, dayInfo.year)
                    property bool isHovered: false
                    
                    color: {
                        if (isSelectedDay) return theme.pink
                        if (isHovered) return theme.blue
                        return "transparent"
                    }

                    Text {
                        anchors.centerIn: parent
                        text: parent.dayNumber
                        color: {
                            if (parent.isSelectedDay) return "#000000"
                            if (parent.isHovered) return "#000000"
                            if (!parent.isCurrentMonth) return theme.textTertiary
                            if (parent.isTodayDay) return theme.pink
                            return theme.textPrimary
                        }
                        font.pixelSize: 10
                        font.family: "JetBrains Mono, monospace"
                        font.weight: parent.isTodayDay || parent.isSelectedDay ? Font.Bold : Font.Normal
                    }
                    
                    MouseArea {
                        id: dayMouseArea
                        anchors.fill: parent
                        hoverEnabled: true

                        onEntered: {
                            parent.isHovered = true
                        }

                        onExited: {
                            parent.isHovered = false
                        }

                        onClicked: {
                            var dayInfo = parent.dayInfo
                            selectedDate = new Date(dayInfo.year, dayInfo.month, dayInfo.day)
                            if (!dayInfo.isCurrentMonth) {
                                currentMonth = dayInfo.month
                                currentYear = dayInfo.year
                            }
                        }
                    }
                    
                    Behavior on color {
                        ColorAnimation { duration: 150 }
                    }
                    
                    Behavior on border.color {
                        ColorAnimation { duration: 150 }
                    }
                }
            }
        }
    }

    Timer {
        interval: 60000
        running: true
        repeat: true
        onTriggered: {
            currentDate = new Date()
        }
    }
}
