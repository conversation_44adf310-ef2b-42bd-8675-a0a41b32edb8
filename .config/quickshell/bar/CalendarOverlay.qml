import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "./modules"

PanelWindow {
    id: calendarOverlay

    property var screen
    property var barWindow
    property bool overlayVisible: false

    visible: overlayVisible

    implicitWidth: 320
    implicitHeight: 240

    WlrLayershell.layer: WlrLayer.Overlay

    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }

    x: {
        if (!barWindow || !screen) return 0

        var screenRect = screen.geometry
        var centerX = screenRect.width / 2 - implicitWidth / 2

        return Math.max(10, Math.min(centerX, screenRect.width - implicitWidth - 10))
    }

    y: 40

    Theme {
        id: theme
    }

    Rectangle {
        id: overlayBackground
        anchors.fill: parent
        radius: 16
        color: theme.backgroundSecondary
        border.color: theme.border
        border.width: 1
        opacity: overlayVisible ? 1.0 : 0.0

        transform: [
            Scale {
                id: scaleTransform
                origin.x: overlayBackground.width / 2
                origin.y: 0
                xScale: overlayVisible ? 1.0 : 0.8
                yScale: overlayVisible ? 1.0 : 0.8

                Behavior on xScale {
                    NumberAnimation {
                        duration: 400
                        easing.type: Easing.OutBack
                        easing.overshoot: 1.1
                    }
                }

                Behavior on yScale {
                    NumberAnimation {
                        duration: 400
                        easing.type: Easing.OutBack
                        easing.overshoot: 1.1
                    }
                }
            },
            Translate {
                id: translateTransform
                y: overlayVisible ? 0 : -20

                Behavior on y {
                    NumberAnimation {
                        duration: 300
                        easing.type: Easing.OutCubic
                    }
                }
            }
        ]

        Behavior on opacity {
            NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
        }

        Behavior on color {
            ColorAnimation { duration: 200 }
        }

        Rectangle {
            anchors.fill: parent
            anchors.margins: -4
            radius: parent.radius + 4
            color: "transparent"
            border.color: Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.3)
            border.width: 1
            z: -1
            opacity: overlayVisible ? 0.6 : 0.0

            Behavior on opacity {
                NumberAnimation { duration: 300 }
            }
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 16

            Text {
                text: "Calendar"
                color: theme.textSecondary
                font.pixelSize: 14
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Medium
                Layout.alignment: Qt.AlignHCenter
            }

            Rectangle {
                id: calendarContainer
                Layout.fillWidth: true
                Layout.fillHeight: true
                radius: 8
                color: theme.backgroundTertiary
                border.color: theme.border
                border.width: 1

                CalendarWidget {
                    id: calendarWidget
                    anchors.fill: parent
                    anchors.margins: 12
                }
            }

            Row {
                Layout.alignment: Qt.AlignHCenter
                spacing: 16

                Text {
                    text: Qt.formatDateTime(new Date(), "dddd, MMMM d, yyyy")
                    color: theme.textPrimary
                    font.pixelSize: 12
                    font.family: "JetBrains Mono, monospace"
                    font.weight: Font.Medium
                }
            }
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                // Prevent closing when clicking inside
            }
        }
    }

    Behavior on x {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }

    Behavior on y {
        NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
    }

    MouseArea {
        anchors.fill: parent
        z: -1
        onClicked: {
            overlayVisible = false
        }
    }

    Timer {
        id: autoHideTimer
        interval: 5000
        onTriggered: {
            overlayVisible = false
        }
    }

    onOverlayVisibleChanged: {
        if (overlayVisible) {
            autoHideTimer.start()
        } else {
            autoHideTimer.stop()
        }
    }
}
