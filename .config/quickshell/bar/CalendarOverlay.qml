import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "./modules"

PanelWindow {
    id: calendarOverlay

    property var screen
    property var barWindow
    property bool overlayVisible: false

    screen: Quickshell.screens[0]

    visible: overlayVisible
    color: "transparent"

    implicitWidth: 400
    implicitHeight: 280

    WlrLayershell.layer: WlrLayer.Overlay

    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }

    function updatePosition() {
        if (screen && screen.geometry) {
            var screenRect = screen.geometry
            var centerX = screenRect.width / 2 - implicitWidth / 2
            x = Math.max(10, Math.min(centerX, screenRect.width - implicitWidth - 10))
            y = 30
        } else {
            x = 200
            y = 30
        }
    }

    Component.onCompleted: updatePosition()

    onBarWindowChanged: updatePosition()
    onScreenChanged: updatePosition()

    Theme {
        id: theme
    }

    Item {
        id: overlayBackground
        anchors.fill: parent
        opacity: overlayVisible ? 1.0 : 0.0

        Canvas {
            id: customShape
            anchors.fill: parent

            onPaint: {
                var ctx = getContext("2d")
                ctx.clearRect(0, 0, width, height)

                var cornerRadius = 20
                var mainWidth = width - 40
                var mainHeight = height - 40
                var startX = 20
                var startY = 0

                ctx.fillStyle = "#000000"

                ctx.beginPath()

                ctx.moveTo(startX, startY)
                ctx.lineTo(startX + mainWidth, startY)
                ctx.lineTo(startX + mainWidth, startY + mainHeight - cornerRadius)
                ctx.quadraticCurveTo(startX + mainWidth, startY + mainHeight, startX + mainWidth - cornerRadius, startY + mainHeight)
                ctx.lineTo(startX + cornerRadius, startY + mainHeight)
                ctx.quadraticCurveTo(startX, startY + mainHeight, startX, startY + mainHeight - cornerRadius)
                ctx.lineTo(startX, startY)

                ctx.closePath()
                ctx.fill()
            }

            Component.onCompleted: requestPaint()
            onVisibleChanged: if (visible) requestPaint()
        }

        transform: [
            Scale {
                id: scaleTransform
                origin.x: overlayBackground.width / 2
                origin.y: 0
                xScale: overlayVisible ? 1.0 : 0.8
                yScale: overlayVisible ? 1.0 : 0.8

                Behavior on xScale {
                    NumberAnimation {
                        duration: 400
                        easing.type: Easing.OutBack
                        easing.overshoot: 1.1
                    }
                }

                Behavior on yScale {
                    NumberAnimation {
                        duration: 400
                        easing.type: Easing.OutBack
                        easing.overshoot: 1.1
                    }
                }
            },
            Translate {
                id: translateTransform
                y: overlayVisible ? 0 : -20

                Behavior on y {
                    NumberAnimation {
                        duration: 300
                        easing.type: Easing.OutCubic
                    }
                }
            }
        ]

        Behavior on opacity {
            NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
        }



        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 40
            anchors.topMargin: 20
            spacing: 16

            Text {
                text: "Calendar"
                color: theme.textSecondary
                font.pixelSize: 14
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Medium
                Layout.alignment: Qt.AlignHCenter
            }

            Rectangle {
                id: calendarContainer
                Layout.fillWidth: true
                Layout.fillHeight: true
                radius: 8
                color: theme.backgroundTertiary
                border.color: theme.border
                border.width: 1

                CalendarWidget {
                    id: calendarWidget
                    anchors.fill: parent
                    anchors.margins: 12
                }
            }

            Row {
                Layout.alignment: Qt.AlignHCenter
                spacing: 16

                Text {
                    text: Qt.formatDateTime(new Date(), "dddd, MMMM d, yyyy")
                    color: theme.textPrimary
                    font.pixelSize: 12
                    font.family: "JetBrains Mono, monospace"
                    font.weight: Font.Medium
                }
            }
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                // Prevent closing when clicking inside
            }
        }
    }



    MouseArea {
        anchors.fill: parent
        z: -1
        onClicked: {
            overlayVisible = false
        }
    }

    Timer {
        id: autoHideTimer
        interval: 5000
        onTriggered: {
            calendarOverlay.overlayVisible = false
        }
    }

    onOverlayVisibleChanged: {
        if (overlayVisible) {
            updatePosition()
        }
    }
}
