import QtQuick
import QtQuick.Layouts
import Quickshell
import Quickshell.Wayland
import "./modules"

PanelWindow {
    id: calendarOverlay

    property var screen
    property var barWindow
    property bool overlayVisible: false

    screen: Quickshell.screens[0]

    visible: true

    onVisibleChanged: {
        console.log("Calendar overlay visible changed:", visible)
    }

    implicitWidth: 320
    implicitHeight: 240

    WlrLayershell.layer: WlrLayer.Top

    anchors {
        top: true
        left: false
        right: false
        bottom: false
    }

    Component.onCompleted: {
        console.log("Calendar overlay component completed")
        console.log("Screen:", screen ? screen.name : "no screen")
        console.log("BarWindow:", barWindow ? "exists" : "no barWindow")
        x = 100
        y = 100
        console.log("Calendar overlay positioned at:", x, y)
    }

    Theme {
        id: theme
    }

    Rectangle {
        id: overlayBackground
        anchors.fill: parent
        radius: 16
        color: overlayVisible ? "#ff0000" : "transparent"
        border.color: "#00ff00"
        border.width: 3
        opacity: overlayVisible ? 1.0 : 0.0

        transform: [
            Scale {
                id: scaleTransform
                origin.x: overlayBackground.width / 2
                origin.y: 0
                xScale: overlayVisible ? 1.0 : 0.8
                yScale: overlayVisible ? 1.0 : 0.8

                Behavior on xScale {
                    NumberAnimation {
                        duration: 400
                        easing.type: Easing.OutBack
                        easing.overshoot: 1.1
                    }
                }

                Behavior on yScale {
                    NumberAnimation {
                        duration: 400
                        easing.type: Easing.OutBack
                        easing.overshoot: 1.1
                    }
                }
            },
            Translate {
                id: translateTransform
                y: overlayVisible ? 0 : -20

                Behavior on y {
                    NumberAnimation {
                        duration: 300
                        easing.type: Easing.OutCubic
                    }
                }
            }
        ]

        Behavior on opacity {
            NumberAnimation { duration: 300; easing.type: Easing.OutCubic }
        }

        Behavior on color {
            ColorAnimation { duration: 200 }
        }

        Rectangle {
            anchors.fill: parent
            anchors.margins: -4
            radius: parent.radius + 4
            color: "transparent"
            border.color: Qt.rgba(theme.accent.r, theme.accent.g, theme.accent.b, 0.3)
            border.width: 1
            z: -1
            opacity: overlayVisible ? 0.6 : 0.0

            Behavior on opacity {
                NumberAnimation { duration: 300 }
            }
        }

        ColumnLayout {
            anchors.fill: parent
            anchors.margins: 20
            spacing: 16

            Text {
                text: "CALENDAR OVERLAY IS VISIBLE!"
                color: "#ffffff"
                font.pixelSize: 20
                font.family: "JetBrains Mono, monospace"
                font.weight: Font.Bold
                Layout.alignment: Qt.AlignHCenter
            }

            Rectangle {
                id: calendarContainer
                Layout.fillWidth: true
                Layout.fillHeight: true
                radius: 8
                color: theme.backgroundTertiary
                border.color: theme.border
                border.width: 1

                CalendarWidget {
                    id: calendarWidget
                    anchors.fill: parent
                    anchors.margins: 12
                }
            }

            Row {
                Layout.alignment: Qt.AlignHCenter
                spacing: 16

                Text {
                    text: Qt.formatDateTime(new Date(), "dddd, MMMM d, yyyy")
                    color: theme.textPrimary
                    font.pixelSize: 12
                    font.family: "JetBrains Mono, monospace"
                    font.weight: Font.Medium
                }
            }
        }

        MouseArea {
            anchors.fill: parent
            onClicked: {
                // Prevent closing when clicking inside
            }
        }
    }



    MouseArea {
        anchors.fill: parent
        z: -1
        onClicked: {
            overlayVisible = false
        }
    }

    Timer {
        id: autoHideTimer
        interval: 5000
        onTriggered: {
            overlayVisible = false
        }
    }

    onOverlayVisibleChanged: {
        console.log("Calendar overlay visibility changed:", overlayVisible)
        if (overlayVisible) {
            autoHideTimer.start()
        } else {
            autoHideTimer.stop()
        }
    }
}
